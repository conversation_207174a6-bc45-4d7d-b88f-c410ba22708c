<template>
  <div class="memory-reminder-container">
    <!-- 记忆时刻 -->
    <div class="memory-section" :class="{ expanded: isMemoryExpanded }">
      <div class="section-header">
        <span class="section-icon">📝</span>
        <span class="section-title">事件记录</span>
        <div class="section-actions">
          <button class="section-add-btn" title="添加" @click="handleMemoryAdd">
            <span class="add-icon">+</span>
          </button>
          <button class="section-expand-btn" :title="isMemoryExpanded ? '收起' : '展开'" @click="toggleMemoryExpanded">
            <div class="triangle-icon" :class="{ expanded: isMemoryExpanded }"></div>
          </button>
        </div>
      </div>
      <div class="section-content" :class="{ expanded: isMemoryExpanded }">
        <div v-if="loadingMemories" class="loading-text">加载中...</div>
        <div v-else-if="memories.length > 0" class="memory-content">
          <!-- 默认只显示一条记录，展开后显示全部 -->
          <div
            v-for="(memory, index) in isMemoryExpanded ? memories : memories.slice(0, 1)"
            :key="memory.event_id"
            class="memory-item"
            @click="handleEditMemory(memory)"
          >
            <div class="memory-header">
              <div class="memory-date">{{ formatMemoryDate(memory.timestamp) }}</div>
              <button class="delete-memory-btn" title="删除" @click.stop="handleDeleteMemory(memory)">
                <span class="delete-icon">×</span>
              </button>
            </div>
            <div class="memory-description">{{ memory.description_text }}</div>
            <div v-if="memory.location" class="memory-location">
              <span class="location-icon">📍</span>
              {{ memory.location }}
            </div>
          </div>
        </div>
        <div v-else class="memory-content">
          <div class="empty-memory">暂无记忆记录，快去和ta聊聊天吧！</div>
        </div>
      </div>
    </div>

    <!-- 提醒事项 - 只在核心节点显示 -->
    <div v-if="isUserProfile" class="reminder-section" :class="{ expanded: isReminderExpanded }">
      <div class="section-header">
        <span class="section-icon">⏰</span>
        <span class="section-title">提醒事项</span>
        <div class="section-actions">
          <button class="section-add-btn" title="添加" @click="handleAddReminder">
            <span class="add-icon">+</span>
          </button>
          <button
            class="section-expand-btn"
            :title="isReminderExpanded ? '收起' : '展开'"
            @click="toggleReminderExpanded"
          >
            <div class="triangle-icon" :class="{ expanded: isReminderExpanded }"></div>
          </button>
        </div>
      </div>
      <div class="section-content" :class="{ expanded: isReminderExpanded }">
        <div v-if="loadingReminders" class="loading-text">加载中...</div>
        <div v-else-if="reminders.length === 0" class="reminder-content">
          <div class="empty-reminder">快来添加提醒事项！</div>
        </div>
        <div v-else class="reminder-list">
          <!-- 默认只显示一条提醒，展开后显示全部 -->
          <div
            v-for="reminder in isReminderExpanded ? reminders : reminders.slice(0, 1)"
            :key="reminder.reminder_id"
            class="reminder-item"
            @click="handleEditReminder(reminder)"
          >
            <div class="reminder-info">
              <div class="reminder-text">{{ reminder.display_text || '提醒事项' }}</div>
            </div>
            <div class="reminder-actions">
              <button class="delete-reminder-btn" @click.stop="handleDeleteReminder(reminder)">
                <span class="delete-icon">×</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import type { IEvent, IReminder, IPersonDetail } from '@/apis/memory';
import { getPersonMemories, getReminders, deleteReminder } from '@/apis/memory';
import { showFailToast, showSuccessToast } from 'vant';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
  isUserProfile: boolean;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  memoryAdd: [];
  editMemory: [memory: IEvent];
  deleteMemory: [memory: IEvent];
  addReminder: [];
  editReminder: [reminder: IReminder];
  deleteReminder: [reminder: IReminder];
}>();

// 响应式数据
const isMemoryExpanded = ref(false);
const isReminderExpanded = ref(false);
const memories = ref<IEvent[]>([]);
const loadingMemories = ref(false);
const reminders = ref<IReminder[]>([]);
const loadingReminders = ref(false);

// 切换记忆时刻展开/收起状态
const toggleMemoryExpanded = () => {
  isMemoryExpanded.value = !isMemoryExpanded.value;
};

// 切换提醒事项展开/收起状态
const toggleReminderExpanded = () => {
  isReminderExpanded.value = !isReminderExpanded.value;
};

// 格式化记忆日期
const formatMemoryDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// 处理记忆时刻添加按钮点击
const handleMemoryAdd = () => {
  emit('memoryAdd');
};

// 处理编辑记忆
const handleEditMemory = (memory: IEvent) => {
  emit('editMemory', memory);
};

// 处理删除记忆
const handleDeleteMemory = (memory: IEvent) => {
  emit('deleteMemory', memory);
};

// 处理添加提醒
const handleAddReminder = () => {
  emit('addReminder');
};

// 处理编辑提醒
const handleEditReminder = (reminder: IReminder) => {
  emit('editReminder', reminder);
};

// 处理删除提醒
const handleDeleteReminder = async (reminder: IReminder) => {
  try {
    console.log('🗑️ [MemoryReminderSection] 开始删除提醒:', reminder);

    const response = await deleteReminder({
      user_id: props.userId,
      reminder_id: reminder.reminder_id,
    });

    if (response && response.success) {
      // 从本地列表中移除
      reminders.value = reminders.value.filter((r) => r.reminder_id !== reminder.reminder_id);
      showSuccessToast('提醒删除成功');
      console.log('✅ [MemoryReminderSection] 提醒删除成功');
    } else {
      showFailToast('提醒删除失败');
      console.error('❌ [MemoryReminderSection] 提醒删除失败:', response);
    }
  } catch (error) {
    console.error('❌ [MemoryReminderSection] 删除提醒失败:', error);
    showFailToast('提醒删除失败');
  }
};

// 加载记忆数据
const loadMemories = async () => {
  if (!props.personId || !props.userId) {
    memories.value = [];
    return;
  }

  try {
    loadingMemories.value = true;
    console.log('🔄 [MemoryReminderSection] 开始获取记忆数据...');

    const response = await getPersonMemories({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [MemoryReminderSection] 记忆数据响应:', response);

    if (response && response.result === 'success' && response.events) {
      memories.value = response.events;
      console.log('✅ [MemoryReminderSection] 记忆数据加载成功，共', memories.value.length, '条记忆');
    } else {
      console.warn('⚠️ [MemoryReminderSection] 记忆数据格式异常:', response);
      memories.value = [];
    }
  } catch (error) {
    console.error('❌ [MemoryReminderSection] 获取记忆数据失败:', error);
    memories.value = [];
  } finally {
    loadingMemories.value = false;
  }
};

// 加载提醒数据
const loadReminders = async () => {
  if (!props.userId || !props.isUserProfile) {
    reminders.value = [];
    return;
  }

  try {
    loadingReminders.value = true;
    console.log('🔄 [MemoryReminderSection] 开始获取提醒数据...');

    const response = await getReminders({ user_id: props.userId });
    console.log('📡 [MemoryReminderSection] 提醒数据响应:', response);

    if (response && response.success) {
      reminders.value = response.reminders || [];
      console.log('✅ [MemoryReminderSection] 提醒数据加载成功，共', reminders.value.length, '条提醒');
    } else {
      console.warn('⚠️ [MemoryReminderSection] 提醒数据格式异常:', response);
      reminders.value = [];
    }
  } catch (error) {
    console.error('❌ [MemoryReminderSection] 获取提醒数据失败:', error);
    reminders.value = [];
  } finally {
    loadingReminders.value = false;
  }
};

// 监听props变化，重新加载数据
watch(
  () => [props.personId, props.userId, props.isUserProfile],
  () => {
    void loadMemories();
    void loadReminders();
  },
  { immediate: true },
);

// 组件挂载时加载数据
onMounted(() => {
  void loadMemories();
  void loadReminders();
});
</script>

<style lang="scss" scoped>
.memory-reminder-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.reminder-section,
.memory-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

// 提醒事项和记忆时刻展开/收起控制
.reminder-section .section-content,
.memory-section .section-content {
  max-height: 260px;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: none;
  }
}

// 记忆时刻禁用滑动
.memory-section .section-content {
  overflow: hidden !important;

  &.expanded {
    overflow: visible !important;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }
}

.section-add-btn,
.section-expand-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid #00bcd4;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }
}

.section-add-btn {
  .add-icon {
    font-size: 20px;
    font-weight: bold;
    color: #00bcd4;
  }
}

.section-expand-btn {
  .triangle-icon {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #00bcd4;
    transition: transform 0.3s ease;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

// 记忆时刻特有样式
.memory-content {
  .memory-item {
    background: rgba(0, 188, 212, 0.05);
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 20px;
    position: relative;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      border-color: rgba(0, 188, 212, 0.5);
      background: rgba(0, 188, 212, 0.08);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);
    }

    .memory-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-right: 40px;

      .memory-date {
        color: rgba(255, 255, 255, 0.8);
        font-size: 26px;
        font-weight: 500;
      }

      .delete-memory-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        background: none;
        border: none;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.6);
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        font-size: 18px;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: #ff6b6b;
          transform: scale(1.1);
        }

        .delete-icon {
          font-size: 30px;
        }
      }
    }

    .memory-description {
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      font-weight: 600;
      line-height: 1.4;
      margin: 8px 0;
    }

    .memory-location {
      color: rgba(255, 255, 255, 0.7);
      font-size: 24px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 8px;

      .location-icon {
        font-size: 22px;
      }
    }
  }

  .empty-memory {
    color: rgba(255, 255, 255, 0.6);
    font-size: 30px;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: rgba(0, 188, 212, 0.05);
    border: 1px dashed rgba(0, 188, 212, 0.3);
    border-radius: 12px;
    line-height: 1.6;
  }
}

// 提醒事项样式
.reminder-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reminder-item {
  background: rgba(0, 188, 212, 0.05);
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 16px;
  padding: 20px;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 188, 212, 0.5);
    background: rgba(0, 188, 212, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);
  }
}

.reminder-info {
  flex: 1;
  padding-right: 40px;

  .reminder-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 12px;
  }
}

.reminder-actions {
  position: absolute;
  top: 4px;
  right: 4px;
}

.delete-reminder-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 18px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #ff6b6b;
    transform: scale(1.1);
  }

  .delete-icon {
    font-size: 30px;
  }
}

.reminder-content {
  .empty-reminder {
    color: rgba(255, 255, 255, 0.6);
    font-size: 30px;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: rgba(0, 188, 212, 0.05);
    border: 1px dashed rgba(0, 188, 212, 0.3);
    border-radius: 12px;
    line-height: 1.6;
  }
}
</style>
